{"files.associations": {"iostream": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__split_buffer": "cpp", "__threading_support": "cpp", "__tree": "cpp", "__verbose_abort": "cpp", "any": "cpp", "array": "cpp", "bitset": "cpp", "cctype": "cpp", "cfenv": "cpp", "charconv": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "complex": "cpp", "condition_variable": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cuchar": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "execution": "cpp", "memory": "cpp", "forward_list": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "mutex": "cpp", "new": "cpp", "optional": "cpp", "ostream": "cpp", "print": "cpp", "queue": "cpp", "ratio": "cpp", "regex": "cpp", "scoped_allocator": "cpp", "set": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stack": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "string_view": "cpp", "tuple": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "valarray": "cpp", "variant": "cpp", "vector": "cpp", "algorithm": "cpp"}}