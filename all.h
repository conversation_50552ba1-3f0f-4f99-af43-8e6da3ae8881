#ifndef ALL_H
#define ALL_H

#include <cassert>
#include <cctype>
#include <cerrno>
#include <cfenv>
#include <cfloat>
#include <cinttypes>
#include <ciso646>
#include <climits>
#include <clocale>
#include <cmath>
#include <csetjmp>
#include <csignal>
#include <cstdarg>
#include <cstdbool>
#include <cstddef>
#include <cstdint>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <ctgmath>
#include <ctime>
#include <cuchar>
#include <cwchar>
#include <cwctype>
#include <iostream>
#include <iomanip>
#include <ios>
#include <iosfwd>
#include <istream>
#include <ostream>
#include <sstream>
#include <streambuf>
#include <fstream>
#include <array>
#include <vector>
#include <deque>
#include <list>
#include <forward_list>
#include <set>
#include <map>
#include <unordered_set>
#include <unordered_map>
#include <stack>
#include <queue>
#include <algorithm>
#include <iterator>
#include <numeric>
#include <random>
#include <ratio>
#include <complex>
#include <string>
#include <regex>
#include <atomic>
#include <thread>
#include <mutex>
#include <shared_mutex>
#include <condition_variable>
#include <future>
#include <chrono>
#include <functional>
#include <type_traits>
#include <typeinfo>
#include <typeindex>
#include <memory>
#include <new>
#include <scoped_allocator>
#include <exception>
#include <stdexcept>
#include <system_error>
#include <utility>
#include <bitset>
#include <tuple>
#include <locale>
#include <codecvt>
#include <initializer_list>
#include <valarray>
#include <limits>
using namespace std;

#endif
