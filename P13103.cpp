#include <bits/stdc++.h>
#define int __int128_t
signed main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    cout.tie(0);
    int n;cin >> n;
    vector<int>a(n+1);
    for (int i=1;i<=n;i++){
        cin >> a[i];
    }
    int l=0,r=1e18;
    auto miku = [&](int x){
        for (int i=1;i<=n;i++){
            if ((x-i+1)<a[i]) return 0;
            if (max(a[i-1],a[i+1]) && (i!=1 || i!=n)) return 0;
            a.erase(a.begin()+i);
        }
    };
    int ans;
    while(l<=r){
        int mid = l+r>>1;
        if (miku(mid)){
            r=mid-1;
            ans = mid;
        }
        else{
            l=mid+1;
        }
    }
    cout << ans;
}