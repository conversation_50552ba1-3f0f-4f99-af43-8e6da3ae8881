<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行开户系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#36D399',
                        neutral: '#F3F4F6',
                        danger: '#F87272',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .form-input-focus {
                @apply focus:ring-2 focus:ring-primary/50 focus:border-primary transition duration-200;
            }
            .btn-hover {
                @apply hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200;
            }
            .card-shadow {
                @apply shadow-md hover:shadow-lg transition-shadow duration-300;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen font-sans">
    <!-- 顶部导航栏 -->
    <header class="bg-primary text-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fa fa-bank text-2xl"></i>
                <h1 class="text-xl font-bold">银行开户系统</h1>
            </div>
            <nav>
                <ul class="flex space-x-6">
                    <li><a href="#" class="hover:text-gray-200 transition-colors duration-200"><i class="fa fa-home mr-1"></i>首页</a></li>
                    <li><a href="#" class="hover:text-gray-200 transition-colors duration-200"><i class="fa fa-info-circle mr-1"></i>关于</a></li>
                    <li><a href="#" class="hover:text-gray-200 transition-colors duration-200"><i class="fa fa-phone mr-1"></i>联系我们</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 开户卡片 -->
        <section class="max-w-3xl mx-auto mb-12">
            <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">欢迎使用在线开户服务</h2>
                    <p class="text-gray-600">请填写以下信息完成开户申请</p>
                </div>
                
                <form id="accountForm" class="space-y-5">
                    <!-- 个人信息 -->
                    <div class="bg-neutral rounded-lg p-5">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                            <i class="fa fa-user-circle text-primary mr-2"></i>个人信息
                        </h3>
                        
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-medium text-gray-700">姓名 <span class="text-danger">*</span></label>
                                <div class="relative">
                                    <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                                        <i class="fa fa-user"></i>
                                    </span>
                                    <input type="text" id="name" name="name" class="pl-10 w-full rounded-md border-gray-300 shadow-sm form-input-focus" placeholder="请输入您的姓名">
                                </div>
                                <p class="error-message text-danger text-sm hidden">请输入2-20个汉字</p>
                            </div>
                            
                            <div class="space-y-2">
                                <label for="idCard" class="block text-sm font-medium text-gray-700">身份证号 <span class="text-danger">*</span></label>
                                <div class="relative">
                                    <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                                        <i class="fa fa-id-card"></i>
                                    </span>
                                    <input type="text" id="idCard" name="idCard" class="pl-10 w-full rounded-md border-gray-300 shadow-sm form-input-focus" placeholder="请输入您的身份证号">
                                </div>
                                <p class="error-message text-danger text-sm hidden">请输入18位有效身份证号</p>
                            </div>
                        </div>
                        
                        <div class="grid md:grid-cols-2 gap-4 mt-4">
                            <div class="space-y-2">
                                <label for="phone" class="block text-sm font-medium text-gray-700">手机号 <span class="text-danger">*</span></label>
                                <div class="relative">
                                    <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                                        <i class="fa fa-phone"></i>
                                    </span>
                                    <input type="tel" id="phone" name="phone" class="pl-10 w-full rounded-md border-gray-300 shadow-sm form-input-focus" placeholder="请输入您的手机号">
                                </div>
                                <p class="error-message text-danger text-sm hidden">请输入有效的11位手机号</p>
                            </div>
                            
                            <div class="space-y-2">
                                <label for="email" class="block text-sm font-medium text-gray-700">邮箱 <span class="text-danger">*</span></label>
                                <div class="relative">
                                    <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                                        <i class="fa fa-envelope"></i>
                                    </span>
                                    <input type="email" id="email" name="email" class="pl-10 w-full rounded-md border-gray-300 shadow-sm form-input-focus" placeholder="请输入您的邮箱">
                                </div>
                                <p class="error-message text-danger text-sm hidden">请输入有效的邮箱地址</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 账户信息 -->
                    <div class="bg-neutral rounded-lg p-5">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                            <i class="fa fa-credit-card text-primary mr-2"></i>账户信息
                        </h3>
                        
                        <div class="space-y-4">
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">账户类型 <span class="text-danger">*</span></label>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                    <div class="relative">
                                        <input type="radio" id="accountType1" name="accountType" value="储蓄账户" class="peer sr-only">
                                        <label for="accountType1" class="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 hover:bg-gray-50 transition-colors duration-200">
                                            <i class="fa fa-money text-primary mr-2"></i>
                                            <span>储蓄账户</span>
                                        </label>
                                    </div>
                                    
                                    <div class="relative">
                                        <input type="radio" id="accountType2" name="accountType" value="支票账户" class="peer sr-only">
                                        <label for="accountType2" class="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 hover:bg-gray-50 transition-colors duration-200">
                                            <i class="fa fa-file-text-o text-primary mr-2"></i>
                                            <span>支票账户</span>
                                        </label>
                                    </div>
                                    
                                    <div class="relative">
                                        <input type="radio" id="accountType3" name="accountType" value="信用账户" class="peer sr-only">
                                        <label for="accountType3" class="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 hover:bg-gray-50 transition-colors duration-200">
                                            <i class="fa fa-credit-card text-primary mr-2"></i>
                                            <span>信用账户</span>
                                        </label>
                                    </div>
                                </div>
                                <p class="error-message text-danger text-sm hidden">请选择账户类型</p>
                            </div>
                            
                            <div class="space-y-2">
                                <label for="initialDeposit" class="block text-sm font-medium text-gray-700">初始存款(元) <span class="text-danger">*</span></label>
                                <div class="relative">
                                    <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                                        <i class="fa fa-rmb"></i>
                                    </span>
                                    <input type="number" id="initialDeposit" name="initialDeposit" min="0" step="0.01" class="pl-10 w-full rounded-md border-gray-300 shadow-sm form-input-focus" placeholder="请输入初始存款金额">
                                </div>
                                <p class="error-message text-danger text-sm hidden">存款金额必须为正数</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="flex justify-center pt-4">
                        <button type="submit" id="submitBtn" class="bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-lg font-medium flex items-center btn-hover">
                            <i class="fa fa-check-circle mr-2"></i>
                            提交开户申请
                        </button>
                    </div>
                </form>
            </div>
        </section>
        
        <!-- 开户成功模态框 -->
        <div id="successModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
                        <i class="fa fa-check text-3xl text-green-500"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-2">开户成功！</h3>
                    <p class="text-gray-600 mb-6">您的账户已成功开立，以下是您的账户信息：</p>
                    
                    <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                        <p class="mb-2"><span class="font-medium text-gray-700">账户ID：</span> <span id="accountId" class="text-primary font-mono"></span></p>
                        <p class="mb-2"><span class="font-medium text-gray-700">开户日期：</span> <span id="openingDate"></span></p>
                        <p class="mb-2"><span class="font-medium text-gray-700">账户类型：</span> <span id="modalAccountType"></span></p>
                        <p><span class="font-medium text-gray-700">初始存款：</span> <span id="modalInitialDeposit"></span> 元</p>
                    </div>
                    
                    <button id="closeModal" class="bg-primary hover:bg-primary/90 text-white px-6 py-2 rounded-lg font-medium btn-hover">
                        完成
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">银行开户系统</h3>
                    <p class="text-gray-400">提供便捷、安全的在线开户服务，让您足不出户即可完成账户开立。</p>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">联系我们</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li class="flex items-center"><i class="fa fa-map-marker w-5 text-gray-300"></i> 北京市朝阳区金融街1号</li>
                        <li class="flex items-center"><i class="fa fa-phone w-5 text-gray-300"></i> ************</li>
                        <li class="flex items-center"><i class="fa fa-envelope w-5 text-gray-300"></i> <EMAIL></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">关注我们</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"><i class="fa fa-weixin text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"><i class="fa fa-weibo text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"><i class="fa fa-qq text-xl"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>© 2025 银行开户系统 版权所有</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('accountForm');
            const successModal = document.getElementById('successModal');
            const modalContent = document.getElementById('modalContent');
            const closeModal = document.getElementById('closeModal');
            
            // 表单验证
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                let isValid = true;
                
                // 验证姓名
                const name = document.getElementById('name').value.trim();
                if (!/^[\u4e00-\u9fa5]{2,20}$/.test(name)) {
                    showError('name', '请输入2-20个汉字');
                    isValid = false;
                } else {
                    hideError('name');
                }
                
                // 验证身份证号
                const idCard = document.getElementById('idCard').value.trim();
                if (!/^\d{17}[\dXx]$/.test(idCard)) {
                    showError('idCard', '请输入18位有效身份证号');
                    isValid = false;
                } else {
                    hideError('idCard');
                }
                
                // 验证手机号
                const phone = document.getElementById('phone').value.trim();
                if (!/^1[3-9]\d{9}$/.test(phone)) {
                    showError('phone', '请输入有效的11位手机号');
                    isValid = false;
                } else {
                    hideError('phone');
                }
                
                // 验证邮箱
                const email = document.getElementById('email').value.trim();
                if (!/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/.test(email)) {
                    showError('email', '请输入有效的邮箱地址');
                    isValid = false;
                } else {
                    hideError('email');
                }
                
                // 验证账户类型
                const accountType = document.querySelector('input[name="accountType"]:checked');
                if (!accountType) {
                    document.querySelector('input[name="accountType"]').closest('.space-y-2').querySelector('.error-message').classList.remove('hidden');
                    isValid = false;
                } else {
                    document.querySelector('input[name="accountType"]').closest('.space-y-2').querySelector('.error-message').classList.add('hidden');
                }
                
                // 验证初始存款
                const initialDeposit = parseFloat(document.getElementById('initialDeposit').value);
                if (isNaN(initialDeposit) || initialDeposit <= 0) {
                    showError('initialDeposit', '存款金额必须为正数');
                    isValid = false;
                } else {
                    hideError('initialDeposit');
                }
                
                // 如果验证通过，显示成功模态框
                if (isValid) {
                    // 生成账户ID
                    const timestamp = new Date().getTime();
                    const accountId = `ACC${timestamp}`;
                    
                    // 获取当前日期
                    const now = new Date();
                    const openingDate = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
                    
                    // 填充模态框数据
                    document.getElementById('accountId').textContent = accountId;
                    document.getElementById('openingDate').textContent = openingDate;
                    document.getElementById('modalAccountType').textContent = accountType.value;
                    document.getElementById('modalInitialDeposit').textContent = initialDeposit.toFixed(2);
                    
                    // 显示模态框
                    successModal.classList.remove('hidden');
                    setTimeout(() => {
                        modalContent.classList.remove('scale-95', 'opacity-0');
                        modalContent.classList.add('scale-100', 'opacity-100');
                    }, 10);
                    
                    // 重置表单
                    form.reset();
                }
            });
            
            // 关闭模态框
            closeModal.addEventListener('click', function() {
                modalContent.classList.remove('scale-100', 'opacity-100');
                modalContent.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    successModal.classList.add('hidden');
                }, 300);
            });
            
            // 显示错误信息
            function showError(fieldId, message) {
                const field = document.getElementById(fieldId);
                const errorMessage = field.parentElement.querySelector('.error-message');
                if (errorMessage) {
                    errorMessage.textContent = message;
                    errorMessage.classList.remove('hidden');
                }
                field.classList.add('border-danger');
                field.classList.remove('border-gray-300');
            }
            
            // 隐藏错误信息
            function hideError(fieldId) {
                const field = document.getElementById(fieldId);
                const errorMessage = field.parentElement.querySelector('.error-message');
                if (errorMessage) {
                    errorMessage.classList.add('hidden');
                }
                field.classList.remove('border-danger');
                field.classList.add('border-gray-300');
            }
            
            // 实时验证
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (this.value.trim() !== '') {
                        hideError(this.id);
                    }
                });
            });
        });
    </script>
</body>
</html>    